#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Example usage of HotmailChecker

WARNING: This is for educational purposes only!
Only use with your own accounts.
"""

from hotmail_checker import Hotmail<PERSON>hecker
import json


def test_single_account():
    """Test a single account."""
    print("=== Single Account Test ===")
    
    checker = HotmailChecker()
    
    # Replace with actual credentials for testing
    username = "<EMAIL>"
    password = "your_password"
    search_key = "github"  # Optional: search for emails containing this word
    
    result = checker.check_account(username, password, search_key)
    
    print("Result:")
    print(json.dumps(result, indent=2, ensure_ascii=False))


def test_multiple_accounts():
    """Test multiple accounts from a list."""
    print("=== Multiple Accounts Test ===")
    
    # Example account list (replace with actual data)
    accounts = [
        {"username": "<EMAIL>", "password": "password1"},
        {"username": "<EMAIL>", "password": "password2"},
    ]
    
    checker = HotmailChecker()
    
    for account in accounts:
        print(f"\nTesting: {account['username']}")
        result = checker.check_account(
            account['username'], 
            account['password'], 
            "important"  # Search for emails with "important"
        )
        
        print(f"Status: {result['status']}")
        if result['status'] == 'Success':
            inbox_info = result.get('inbox_info', {})
            print(f"Display Name: {inbox_info.get('display_name', 'N/A')}")
            print(f"Total Messages: {inbox_info.get('total_count', 'N/A')}")
            print(f"Unread Messages: {inbox_info.get('unread_count', 'N/A')}")
            
            search_results = result.get('search_results', {})
            if search_results.get('has_messages'):
                print(f"Search Results: {search_results.get('total_results', 0)} messages found")
            else:
                print("No messages found for search term")
        else:
            print(f"Error: {result.get('message', 'Unknown error')}")


def interactive_test():
    """Interactive test mode."""
    print("=== Interactive Test Mode ===")
    print("WARNING: Only use with your own accounts!")
    
    checker = HotmailChecker()
    
    while True:
        print("\n" + "="*50)
        username = input("Enter email (or 'quit' to exit): ").strip()
        
        if username.lower() == 'quit':
            break
            
        if not username:
            print("Please enter a valid email address.")
            continue
            
        password = input("Enter password: ").strip()
        if not password:
            print("Please enter a password.")
            continue
            
        search_key = input("Enter search keyword (optional): ").strip()
        
        print(f"\nChecking account: {username}")
        print("Please wait...")
        
        result = checker.check_account(username, password, search_key)
        
        print(f"\nStatus: {result['status']}")
        
        if result['status'] == 'Success':
            print("✅ Login successful!")
            
            # Display inbox info
            inbox_info = result.get('inbox_info', {})
            if inbox_info.get('status') == 'Success':
                print(f"👤 Name: {inbox_info.get('display_name', 'N/A')}")
                print(f"📧 Total Messages: {inbox_info.get('total_count', 'N/A')}")
                print(f"📬 Unread Messages: {inbox_info.get('unread_count', 'N/A')}")
            
            # Display search results
            if search_key:
                search_results = result.get('search_results', {})
                if search_results.get('status') == 'Success':
                    if search_results.get('has_messages'):
                        print(f"🔍 Search '{search_key}': {search_results.get('total_results', 0)} messages found")
                        if search_results.get('last_delivery'):
                            print(f"📅 Last message: {search_results.get('last_delivery')}")
                        if search_results.get('subject'):
                            print(f"📝 Subject: {search_results.get('subject')}")
                    else:
                        print(f"🔍 Search '{search_key}': No messages found")
                        
        elif result['status'] == '2FACTOR':
            print("🔐 Two-factor authentication required")
        elif result['status'] == 'Failure':
            print(f"❌ Login failed: {result.get('message', 'Invalid credentials')}")
        else:
            print(f"⚠️ {result['status']}: {result.get('message', 'Unknown error')}")


if __name__ == "__main__":
    print("Hotmail/Outlook Checker - Example Usage")
    print("=" * 50)
    print("WARNING: This tool is for educational purposes only!")
    print("Do not use this to access accounts without permission.")
    print("Only test with your own accounts.")
    print("=" * 50)
    
    while True:
        print("\nSelect test mode:")
        print("1. Interactive test")
        print("2. Single account test")
        print("3. Multiple accounts test")
        print("4. Exit")
        
        choice = input("\nEnter your choice (1-4): ").strip()
        
        if choice == '1':
            interactive_test()
        elif choice == '2':
            test_single_account()
        elif choice == '3':
            test_multiple_accounts()
        elif choice == '4':
            print("Goodbye!")
            break
        else:
            print("Invalid choice. Please enter 1, 2, 3, or 4.")
