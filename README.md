# Hotmail/Outlook Kontrol Aracı

Bu proje, OpenBullet konfigürasyon dosyasından Python'a çevrilmiş bir Hotmail/Outlook hesap kontrol aracıdır.

## ⚠️ ÖNEMLİ UYARI

**Bu araç sadece eğitim amaçlıdır!**
- <PERSON><PERSON>e kendi hesaplarınızla test edin
- Başkalarının hesaplarına izinsiz erişim yasaktır
- Bu aracı kötü amaçlı kullanmak yasalara aykırıdır
- Kullanımdan doğacak sorumluluk kullanıcıya aittir

## 📋 Özellikler

- ✅ Hotmail/Outlook hesap doğrulama
- ✅ Gelen kutusu bilgilerini alma (toplam mesaj, okunmamış mesaj)
- ✅ E-posta arama (belirli kelimeler için)
- ✅ Toplu hesap kontrolü (TXT dosyasından)
- ✅ Türkçe arayüz
- ✅ Detaylı hata raporlama
- ✅ JSON formatında sonuç kaydetme

## 📦 Kurulum

### Gereksinimler
- Python 3.7 veya üzeri
- pip (Python paket yöneticisi)

### Adımlar

1. **Projeyi indirin:**
```bash
git clone <repository-url>
cd hotmail-checker
```

2. **Gerekli paketleri yükleyin:**
```bash
pip install -r requirements.txt
```

## 🚀 Kullanım

### 1. Tek Hesap Kontrolü

```bash
python hotmail_checker.py
```

Program sizden şunları isteyecek:
- E-posta adresi
- Şifre
- Arama kelimesi (isteğe bağlı)

### 2. Toplu Hesap Kontrolü

```bash
python toplu_kontrol.py
```

#### TXT Dosyası Formatı

Hesap listesi için `hesaplar.txt` dosyası oluşturun:

```
# Hotmail/Outlook Hesap Listesi
# Format: email:sifre
# Her satırda bir hesap

<EMAIL>:sifre123
<EMAIL>:sifre456
<EMAIL>:sifre789
```

**Not:** `#` ile başlayan satırlar yorum satırıdır ve göz ardı edilir.

## 📊 Çıktı Formatları

### Başarılı Giriş
```json
{
  "status": "Success",
  "username": "<EMAIL>",
  "message": "Giriş başarılı",
  "inbox_info": {
    "display_name": "Örnek Kullanıcı",
    "total_count": "150",
    "unread_count": "5"
  },
  "search_results": {
    "has_messages": true,
    "total_results": "3",
    "search_key": "GITHUB"
  }
}
```

### Başarısız Giriş
```json
{
  "status": "Failure",
  "username": "<EMAIL>",
  "message": "Hesap adı veya şifre yanlış"
}
```

## 🔧 Yapılandırma

### Bekleme Süreleri
Toplu kontrol yaparken hesaplar arası bekleme süresi ayarlayabilirsiniz:
- Varsayılan: 2 saniye
- Önerilen: 2-5 saniye (hesap bloklanmasını önlemek için)

### Proxy Desteği
Gerekirse `requests.Session` nesnesine proxy ayarları ekleyebilirsiniz.

## 📁 Dosya Yapısı

```
hotmail-checker/
├── hotmail_checker.py      # Ana kontrol sınıfı
├── toplu_kontrol.py        # Toplu kontrol aracı
├── requirements.txt        # Python gereksinimleri
├── README.md              # Bu dosya
├── ornek_hesaplar.txt     # Örnek hesap listesi
└── kontrol_sonuclari_*.json  # Sonuç dosyaları
```

## 🛠️ Geliştirme

### Sınıf Yapısı

#### HotmailKontrolcu
Ana kontrol sınıfı. Temel metodlar:
- `hesap_kontrol_et()`: Tek hesap kontrolü
- `gelen_kutusu_bilgisi_al()`: Gelen kutusu bilgileri
- `eposta_ara()`: E-posta arama

#### TopluKontrolcu
Toplu kontrol için yardımcı sınıf:
- `txt_dosyasi_oku()`: TXT dosyasından hesap okuma
- `toplu_kontrol()`: Toplu kontrol işlemi
- `sonuclari_kaydet()`: Sonuçları JSON'a kaydetme

### Hata Yönetimi

Program şu hata türlerini yakalar:
- Ağ bağlantı hataları
- Geçersiz kimlik bilgileri
- 2FA gerektiren hesaplar
- Geçici hesap bloklamaları

## 🔍 Sorun Giderme

### Yaygın Hatalar

1. **"İlk istek başarısız"**
   - İnternet bağlantınızı kontrol edin
   - Proxy ayarlarını kontrol edin

2. **"Çok fazla giriş denemesi"**
   - Bekleme süresini artırın
   - Farklı IP adresi kullanın

3. **"2FA Gerekli"**
   - İki faktörlü doğrulama açık hesaplar desteklenmez
   - 2FA'yı kapatın veya uygulama şifresi kullanın

### Log Dosyaları

Program çalışırken detaylı loglar konsola yazdırılır. Hata ayıklama için bu logları kontrol edin.

## 📝 Lisans

Bu proje eğitim amaçlı olarak geliştirilmiştir. Ticari kullanım için izin gereklidir.

## 🤝 Katkıda Bulunma

1. Fork yapın
2. Feature branch oluşturun (`git checkout -b feature/yeni-ozellik`)
3. Değişikliklerinizi commit edin (`git commit -am 'Yeni özellik eklendi'`)
4. Branch'inizi push edin (`git push origin feature/yeni-ozellik`)
5. Pull Request oluşturun

## 📞 İletişim

Sorularınız için:
- GitHub Issues kullanın
- E-posta: [email korunmuş]

---

**Yasal Uyarı:** Bu araç sadece eğitim ve test amaçlıdır. Kötü niyetli kullanımdan doğacak sorumluluk kullanıcıya aittir.
